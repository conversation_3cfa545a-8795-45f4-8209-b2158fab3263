<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Generator</title>
</head>
<body>
    <h1>Generate Test PNG</h1>
    <canvas id="testCanvas" width="200" height="200" style="border: 1px solid black;"></canvas>
    <br><br>
    <button onclick="generateTestImage()">Generate Test PNG</button>
    <br><br>
    <a id="downloadLink" style="display: none;">Download Test PNG</a>

    <script>
        function generateTestImage() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas with white background
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 200, 200);
            
            // Draw a simple black shape
            ctx.fillStyle = 'black';
            
            // Draw a circle
            ctx.beginPath();
            ctx.arc(100, 100, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw a rectangle
            ctx.fillRect(50, 150, 100, 30);
            
            // Convert to PNG and create download link
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.getElementById('downloadLink');
                link.href = url;
                link.download = 'test-image.png';
                link.textContent = 'Download Test PNG';
                link.style.display = 'block';
            }, 'image/png');
        }
    </script>
</body>
</html>
