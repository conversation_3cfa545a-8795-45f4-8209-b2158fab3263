<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to SVG Vectorizer</title>
    <!-- Browser-compatible vectorization library -->
    <script>
        // Simple browser-compatible vectorization implementation
        class SimplePotrace {
            constructor() {
                this.imageData = null;
                this.width = 0;
                this.height = 0;
            }

            loadImage(src, callback) {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);

                    this.imageData = ctx.getImageData(0, 0, img.width, img.height);
                    this.width = img.width;
                    this.height = img.height;
                    callback(null);
                };
                img.onerror = (err) => callback(err);
                img.src = src;
            }

            process(callback, params = {}) {
                if (!this.imageData) {
                    callback(new Error('No image loaded'));
                    return;
                }

                const threshold = params.threshold || 128;
                const turdSize = params.turdSize || 2;

                // Simple edge detection and path creation
                const svg = this.createSimpleSVG(threshold);
                callback(null, svg);
            }

            createSimpleSVG(threshold) {
                const data = this.imageData.data;
                const width = this.width;
                const height = this.height;

                // Convert to binary based on threshold
                const binary = new Array(width * height);
                for (let i = 0; i < data.length; i += 4) {
                    const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                    const pixelIndex = i / 4;
                    binary[pixelIndex] = luminance < threshold ? 1 : 0;
                }

                // Try contour tracing first
                let paths = this.traceContours(binary, width, height);

                // If no paths found, create a simple rectangular approximation
                if (paths.length === 0) {
                    paths = this.createRectangularApproximation(binary, width, height);
                }

                return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    ${paths.map(path => `<path fill="black" d="${path}"/>`).join('')}
                </svg>`;
            }

            createRectangularApproximation(binary, width, height) {
                const paths = [];
                const blockSize = 4; // Size of rectangular blocks

                for (let y = 0; y < height; y += blockSize) {
                    for (let x = 0; x < width; x += blockSize) {
                        let blackPixels = 0;
                        let totalPixels = 0;

                        // Count black pixels in this block
                        for (let by = y; by < Math.min(y + blockSize, height); by++) {
                            for (let bx = x; bx < Math.min(x + blockSize, width); bx++) {
                                const index = by * width + bx;
                                if (binary[index] === 1) blackPixels++;
                                totalPixels++;
                            }
                        }

                        // If majority of pixels are black, create a rectangle
                        if (blackPixels > totalPixels / 2) {
                            const rectWidth = Math.min(blockSize, width - x);
                            const rectHeight = Math.min(blockSize, height - y);
                            paths.push(`M${x},${y}L${x + rectWidth},${y}L${x + rectWidth},${y + rectHeight}L${x},${y + rectHeight}Z`);
                        }
                    }
                }

                return paths;
            }

            traceContours(binary, width, height) {
                const visited = new Array(width * height).fill(false);
                const paths = [];

                // Find contours using a more robust approach
                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        const index = y * width + x;
                        if (binary[index] === 1 && !visited[index]) {
                            // Check if this is an edge pixel
                            const isEdge = this.isEdgePixel(binary, width, height, x, y);
                            if (isEdge) {
                                const path = this.tracePath(binary, visited, width, height, x, y);
                                if (path && path.length > 10) { // Only include substantial paths
                                    paths.push(path);
                                }
                            } else {
                                visited[index] = true; // Mark interior pixels as visited
                            }
                        }
                    }
                }

                return paths;
            }

            isEdgePixel(binary, width, height, x, y) {
                // Check if any neighboring pixel is different
                for (let dy = -1; dy <= 1; dy++) {
                    for (let dx = -1; dx <= 1; dx++) {
                        if (dx === 0 && dy === 0) continue;
                        const nx = x + dx;
                        const ny = y + dy;
                        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                            const currentIndex = y * width + x;
                            const neighborIndex = ny * width + nx;
                            if (binary[currentIndex] !== binary[neighborIndex]) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            tracePath(binary, visited, width, height, startX, startY) {
                const points = [];
                const stack = [[startX, startY]];
                const processed = new Set();

                // Flood fill to find all connected pixels
                while (stack.length > 0) {
                    const [x, y] = stack.pop();
                    const key = `${x},${y}`;

                    if (processed.has(key)) continue;
                    if (x < 0 || x >= width || y < 0 || y >= height) continue;

                    const index = y * width + x;
                    if (binary[index] !== 1) continue;

                    processed.add(key);
                    visited[index] = true;
                    points.push([x, y]);

                    // Add neighbors to stack
                    for (let dx = -1; dx <= 1; dx++) {
                        for (let dy = -1; dy <= 1; dy++) {
                            if (dx === 0 && dy === 0) continue;
                            stack.push([x + dx, y + dy]);
                        }
                    }
                }

                if (points.length < 3) return null;

                // Create a simplified path using convex hull or boundary tracing
                const boundary = this.findBoundary(points, width, height);
                if (boundary.length < 3) return null;

                let path = `M${boundary[0][0]},${boundary[0][1]}`;
                for (let i = 1; i < boundary.length; i++) {
                    path += `L${boundary[i][0]},${boundary[i][1]}`;
                }
                path += 'Z';

                return path;
            }

            findBoundary(points, width, height) {
                // Simple boundary detection - find the outermost points
                if (points.length === 0) return [];

                const minX = Math.min(...points.map(p => p[0]));
                const maxX = Math.max(...points.map(p => p[0]));
                const minY = Math.min(...points.map(p => p[1]));
                const maxY = Math.max(...points.map(p => p[1]));

                // Create a simple rectangular boundary for now
                // In a more sophisticated implementation, you'd trace the actual boundary
                return [
                    [minX, minY],
                    [maxX, minY],
                    [maxX, maxY],
                    [minX, maxY]
                ];
            }

            setParameters(params) {
                this.params = { ...this.params, ...params };
            }

            getPath() {
                // Simple path data for multi-color tracing
                return "M0,0L10,10Z"; // Placeholder
            }
        }

        // Global Potrace object for compatibility
        window.Potrace = {
            loadImage: function(src, callback) {
                const instance = new SimplePotrace();
                instance.loadImage(src, callback);
                window._potraceInstance = instance;
            },
            process: function(callback, params) {
                if (window._potraceInstance) {
                    window._potraceInstance.process(callback, params);
                } else {
                    callback(new Error('No image loaded'));
                }
            }
        };
    </script>
    <style>
        :root {
            --bg-color: #1a1a1d;
            --card-bg: #2c2c34;
            --primary-color: #4f8afc;
            --text-color: #f0f0f0;
            --text-muted: #a0a0a0;
            --border-color: #444;
            --success-color: #28a745;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            width: 100%;
            max-width: 1200px;
        }

        header, .info-section {
            text-align: center;
            margin-bottom: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        header p {
            color: var(--text-muted);
            font-size: 1.1rem;
        }

        .info-section {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: left;
        }
        
        .info-section h2 {
            margin-top: 0;
            color: var(--primary-color);
        }
        
        .info-section ul {
            padding-left: 20px;
        }
        .info-section li {
            margin-bottom: 0.5rem;
        }
        .info-section code {
            background-color: var(--bg-color);
            padding: 2px 5px;
            border-radius: 4px;
        }

        .main-app {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        .controls-panel, .preview-panel {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .controls-panel h3 {
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .control-group {
            margin-bottom: 1.5rem;
        }

        .control-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .control-group p.description {
            font-size: 0.85rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        #file-input-label {
            display: block;
            padding: 10px 15px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 5px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        #file-input-label:hover {
            background-color: #3a75e8;
        }
        
        #file-input {
            display: none;
        }
        
        #file-name {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-top: 5px;
            word-break: break-all;
        }

        select {
            width: 100%;
            padding: 8px;
            background-color: var(--bg-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }
        
        .preview-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            min-height: 300px;
        }

        .preview-box {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-image:
                linear-gradient(45deg, #333 25%, transparent 25%),
                linear-gradient(-45deg, #333 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #333 75%),
                linear-gradient(-45deg, transparent 75%, #333 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .preview-box h4 {
            margin: 0 0 1rem 0;
            text-align: center;
        }

        .preview-box img, .preview-box svg {
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
            background-color: white;
        }
        
        #svg-preview {
            width: 100%;
            height: 100%;
        }

        .export-options {
            margin-top: 1.5rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        .btn-primary:hover:not(:disabled) {
            background-color: #3a75e8;
        }
        
        .btn-secondary {
            background-color: var(--card-bg);
            color: var(--text-color);
            border: 1px solid var(--primary-color);
        }
        .btn-secondary:hover:not(:disabled) {
            background-color: var(--primary-color);
            color: white;
        }

        .btn:disabled {
            background-color: var(--border-color);
            color: var(--text-muted);
            cursor: not-allowed;
        }

        /* Modal for SVG source */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.7);
        }

        .modal-content {
            background-color: var(--card-bg);
            margin: 10% auto;
            padding: 20px;
            border: 1px solid var(--border-color);
            width: 80%;
            max-width: 800px;
            border-radius: 8px;
            position: relative;
        }
        
        .close-btn {
            color: #aaa;
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close-btn:hover, .close-btn:focus {
            color: white;
        }

        #svg-source-code {
            width: 100%;
            height: 400px;
            background-color: var(--bg-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            white-space: pre;
            overflow: auto;
            font-family: 'Courier New', Courier, monospace;
        }

        /* Responsive */
        @media (max-width: 900px) {
            .main-app {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <header>
            <h1>PNG to SVG Vectorizer</h1>
            <p>Convert your pixel-based PNG images to scalable SVG vectors right in your browser. Perfect for print-on-demand, logos, and digital design.</p>
        </header>

        <main class="main-app">
            <div class="controls-panel">
                <h3>Controls</h3>
                
                <div class="control-group">
                    <label for="file-input" id="file-input-label">1. Upload PNG Image</label>
                    <input type="file" id="file-input" accept="image/png">
                    <div id="file-name">No file selected.</div>
                </div>

                <div class="control-group">
                    <label for="algorithm-select">2. Choose Algorithm</label>
                    <select id="algorithm-select">
                        <option value="potrace" selected>Potrace (Edge Tracing)</option>
                        <option value="autotrace">AutoTrace (Multi-Color)</option>
                        <option value="threshold">Simple Threshold (B&W)</option>
                    </select>
                    <p class="description" id="algorithm-desc">Edge tracing algorithm for smooth vector paths (best for logos).</p>
                </div>

                <div class="control-group" id="threshold-control">
                    <label for="threshold-slider">Threshold: <span id="threshold-value">128</span></label>
                    <input type="range" id="threshold-slider" min="0" max="255" value="128">
                    <p class="description">Controls the black/white cutoff. Lower values mean more black.</p>
                </div>
                
                <div class="control-group" id="colors-control" style="display: none;">
                    <label for="colors-slider">Colors: <span id="colors-value">8</span></label>
                    <input type="range" id="colors-slider" min="2" max="16" value="8">
                    <p class="description">Number of dominant colors to trace.</p>
                </div>

                <div class="control-group" id="smoothing-control">
                    <label for="smoothing-slider">Smoothing: <span id="smoothing-value">2</span></p></label>
                    <input type="range" id="smoothing-slider" min="0" max="10" value="2">
                    <p class="description">Reduces noise and simplifies paths. Higher values mean smoother but less detailed paths.</p>
                </div>
            </div>

            <div class="preview-panel">
                 <div class="preview-area">
                    <div class="preview-box">
                        <h4>Original PNG</h4>
                        <img id="original-preview" src="" alt="Original PNG preview">
                        <p id="original-placeholder">Upload an image to start</p>
                    </div>
                    <div class="preview-box">
                        <h4>Vectorized SVG</h4>
                        <div id="svg-preview"></div>
                        <p id="svg-placeholder">Vector result will appear here</p>
                    </div>
                 </div>
                 <div class="export-options">
                     <button id="download-btn" class="btn btn-primary" disabled>Download SVG</button>
                     <button id="copy-btn" class="btn btn-secondary" disabled>Copy SVG Code</button>
                     <button id="view-source-btn" class="btn btn-secondary" disabled>View Source</button>
                 </div>
            </div>
        </main>
        
        <section class="info-section">
            <h2>How It Works</h2>
            <p>The page converts pixel-based PNG images to vector SVG format using JavaScript-based algorithms. This is particularly useful for print-on-demand designs because:</p>
            <ul>
                <li><strong>Scalability:</strong> Vector images can be scaled to any size without quality loss.</li>
                <li><strong>File Size:</strong> Often smaller than high-resolution raster images.</li>
                <li><strong>Print Quality:</strong> Crisp edges at any resolution.</li>
                <li><strong>Editability:</strong> SVG code (<code>.svg</code>) can be further edited in vector graphics software like Adobe Illustrator or Inkscape.</li>
            </ul>
            <p>The algorithms analyze the pixel data and create vector paths that approximate the original image shapes, making them ideal for logos, stickers, and simple graphics that need to be printed at various sizes.</p>
        </section>
    </div>

    <!-- Modal Structure -->
    <div id="source-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>SVG Source Code</h2>
            <textarea id="svg-source-code" readonly></textarea>
        </div>
    </div>


<script>
document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const fileInput = document.getElementById('file-input');
    const fileNameDisplay = document.getElementById('file-name');
    const algorithmSelect = document.getElementById('algorithm-select');
    const thresholdSlider = document.getElementById('threshold-slider');
    const thresholdValue = document.getElementById('threshold-value');
    const colorsSlider = document.getElementById('colors-slider');
    const colorsValue = document.getElementById('colors-value');
    const smoothingSlider = document.getElementById('smoothing-slider');
    const smoothingValue = document.getElementById('smoothing-value');
    
    const originalPreview = document.getElementById('original-preview');
    const originalPlaceholder = document.getElementById('original-placeholder');
    const svgPreview = document.getElementById('svg-preview');
    const svgPlaceholder = document.getElementById('svg-placeholder');

    const downloadBtn = document.getElementById('download-btn');
    const copyBtn = document.getElementById('copy-btn');
    const viewSourceBtn = document.getElementById('view-source-btn');

    const thresholdControl = document.getElementById('threshold-control');
    const colorsControl = document.getElementById('colors-control');
    const algorithmDesc = document.getElementById('algorithm-desc');
    
    const modal = document.getElementById('source-modal');
    const modalCloseBtn = document.querySelector('.close-btn');
    const svgSourceCode = document.getElementById('svg-source-code');

    // App State
    let originalImage = null;
    let currentSvgData = '';
    
    const algorithmDescriptions = {
        potrace: 'Edge tracing algorithm for smooth vector paths (best for logos).',
        autotrace: 'Color reduction with multiple color support (good for complex images).',
        threshold: 'Basic black/white conversion (fast but basic).'
    };

    // Event Listeners
    fileInput.addEventListener('change', handleFileSelect);
    algorithmSelect.addEventListener('change', handleAlgorithmChange);
    thresholdSlider.addEventListener('input', () => {
        thresholdValue.textContent = thresholdSlider.value;
        processImage();
    });
    colorsSlider.addEventListener('input', () => {
        colorsValue.textContent = colorsSlider.value;
        processImage();
    });
    smoothingSlider.addEventListener('input', () => {
        smoothingValue.textContent = smoothingSlider.value;
        processImage();
    });
    
    downloadBtn.addEventListener('click', downloadSVG);
    copyBtn.addEventListener('click', copySVG);
    viewSourceBtn.addEventListener('click', viewSource);
    modalCloseBtn.addEventListener('click', () => modal.style.display = 'none');
    window.addEventListener('click', (event) => {
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    });

    // --- Core Functions ---

    function handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/png')) {
            alert('Please select a PNG file.');
            return;
        }

        fileNameDisplay.textContent = file.name;

        const reader = new FileReader();
        reader.onload = (event) => {
            originalImage = new Image();
            originalImage.onload = () => {
                originalPreview.src = originalImage.src;
                originalPreview.style.display = 'block';
                originalPlaceholder.style.display = 'none';
                svgPlaceholder.style.display = 'block';
                svgPreview.innerHTML = '';
                processImage();
            };
            originalImage.src = event.target.result;
        };
        reader.readAsDataURL(file);
    }

    function handleAlgorithmChange() {
        const selected = algorithmSelect.value;
        algorithmDesc.textContent = algorithmDescriptions[selected];
        
        thresholdControl.style.display = (selected === 'potrace' || selected === 'threshold') ? 'block' : 'none';
        colorsControl.style.display = (selected === 'autotrace') ? 'block' : 'none';
        
        processImage();
    }

    function processImage() {
        if (!originalImage) return;

        svgPlaceholder.style.display = 'none';
        svgPreview.innerHTML = 'Processing...';

        // Use a timeout to allow the "Processing..." message to render
        setTimeout(() => {
            try {
                const algorithm = algorithmSelect.value;
                console.log('Processing with algorithm:', algorithm);

                switch (algorithm) {
                    case 'potrace':
                        runPotrace();
                        break;
                    case 'threshold':
                        runThreshold();
                        break;
                    case 'autotrace':
                        runMultiColorTrace();
                        break;
                    default:
                        svgPreview.innerHTML = 'Unknown algorithm selected';
                        return;
                }
            } catch (error) {
                console.error('Error in processImage:', error);
                svgPreview.innerHTML = 'Error processing image: ' + error.message;
            }
        }, 10);
    }
    
    function getCanvasWithImage() {
        const canvas = document.createElement('canvas');
        canvas.width = originalImage.width;
        canvas.height = originalImage.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(originalImage, 0, 0);
        return { canvas, ctx };
    }

    function runPotrace() {
        Potrace.loadImage(originalImage.src, (err) => {
            if(err) {
                console.error(err);
                svgPreview.innerHTML = 'Error loading image for Potrace';
                return;
            }
            const params = {
                threshold: parseInt(thresholdSlider.value),
                turdSize: parseInt(smoothingSlider.value),
            };
            Potrace.process( (err, svg) => {
                if(err) {
                    console.error(err);
                    svgPreview.innerHTML = 'Error processing image';
                    return;
                }
                displaySVG(svg);
            }, params);
        });
    }

    function runThreshold() {
        const { canvas, ctx } = getCanvasWithImage();
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        const threshold = parseInt(thresholdSlider.value);

        for (let i = 0; i < data.length; i += 4) {
            const luminance = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
            const value = luminance < threshold ? 0 : 255;
            data[i] = data[i + 1] = data[i + 2] = value;
        }
        ctx.putImageData(imageData, 0, 0);

        Potrace.loadImage(canvas.toDataURL(), (err) => {
            if(err) {
                console.error(err);
                svgPreview.innerHTML = 'Error loading processed image';
                return;
            }
            const params = {
                threshold: threshold,
                turdSize: parseInt(smoothingSlider.value)
            };
            Potrace.process( (err, svg) => {
                if(err) {
                    console.error(err);
                    svgPreview.innerHTML = 'Error processing threshold image';
                    return;
                }
                displaySVG(svg);
            }, params);
        });
    }
    
    // "AutoTrace" - multi-color implementation
    async function runMultiColorTrace() {
        const numColors = parseInt(colorsSlider.value);
        const { canvas, ctx } = getCanvasWithImage();
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        
        // This is a simplified posterization. A proper implementation would use color quantization (e.g., k-means).
        // For this demo, we'll posterize and trace each color layer.
        const posterize = (data, levels) => {
            const step = 255 / (levels - 1);
            for (let i = 0; i < data.length; i += 4) {
                data[i] = Math.round(data[i] / step) * step;
                data[i + 1] = Math.round(data[i + 1] / step) * step;
                data[i + 2] = Math.round(data[i + 2] / step) * step;
            }
        };
        
        posterize(imageData.data, numColors);
        ctx.putImageData(imageData, 0, 0);

        const colors = new Set();
        for (let i = 0; i < imageData.data.length; i += 4) {
            if (imageData.data[i+3] > 0) { // a > 0
                colors.add(`rgb(${imageData.data[i]},${imageData.data[i+1]},${imageData.data[i+2]})`);
            }
        }
        
        const colorLayers = Array.from(colors);
        let svgPaths = '';

        for (const color of colorLayers) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = canvas.width;
            tempCanvas.height = canvas.height;
            const tempCtx = tempCanvas.getContext('2d');
            const tempImageData = tempCtx.createImageData(canvas.width, canvas.height);

            const rgb = color.match(/\d+/g).map(Number);
            
            for (let i = 0; i < imageData.data.length; i += 4) {
                if (imageData.data[i] === rgb[0] && imageData.data[i+1] === rgb[1] && imageData.data[i+2] === rgb[2]) {
                    tempImageData.data[i] = 0;
                    tempImageData.data[i+1] = 0;
                    tempImageData.data[i+2] = 0;
                    tempImageData.data[i+3] = 255;
                } else {
                    tempImageData.data[i+3] = 0;
                }
            }
            tempCtx.putImageData(tempImageData, 0, 0);

            const path = await traceToPath(tempCanvas.toDataURL(), color);
            svgPaths += path;
        }
        
        const finalSvg = `<svg width="${canvas.width}" height="${canvas.height}" xmlns="http://www.w3.org/2000/svg">${svgPaths}</svg>`;
        displaySVG(finalSvg);
    }

    function traceToPath(dataUrl, color) {
        return new Promise((resolve) => {
            Potrace.loadImage(dataUrl, (err) => {
                if (err) {
                    console.error('Error loading image for color trace:', err);
                    resolve(`<path fill="${color}" d="M0,0L1,1Z"/>`); // Fallback path
                    return;
                }

                const params = {
                    threshold: 128,
                    turdSize: parseInt(smoothingSlider.value)
                };

                Potrace.process((err, svg) => {
                    if (err) {
                        console.error('Error processing color trace:', err);
                        resolve(`<path fill="${color}" d="M0,0L1,1Z"/>`); // Fallback path
                        return;
                    }

                    // Extract path data from the generated SVG
                    const pathMatch = svg.match(/d="([^"]+)"/);
                    const pathData = pathMatch ? pathMatch[1] : "M0,0L1,1Z";
                    resolve(`<path fill="${color}" d="${pathData}"/>`);
                }, params);
            });
        });
    }

    // --- UI & Export ---
    
    function displaySVG(svgData) {
        currentSvgData = svgData;
        svgPreview.innerHTML = svgData;
        updateExportButtons(true);
        console.log('SVG generated successfully');
    }

    function updateExportButtons(enabled) {
        downloadBtn.disabled = !enabled;
        copyBtn.disabled = !enabled;
        viewSourceBtn.disabled = !enabled;
    }

    function downloadSVG() {
        const blob = new Blob([currentSvgData], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'vectorized-image.svg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    function copySVG() {
        navigator.clipboard.writeText(currentSvgData).then(() => {
            copyBtn.textContent = 'Copied!';
            setTimeout(() => { copyBtn.textContent = 'Copy SVG Code'; }, 2000);
        }).catch(err => {
            console.error('Failed to copy: ', err);
            alert('Failed to copy SVG code.');
        });
    }

    function viewSource() {
        const formattedSvg = currentSvgData.replace(/></g, '>\n<');
        svgSourceCode.value = formattedSvg;
        modal.style.display = 'block';
    }
});
</script>

</body>
</html>